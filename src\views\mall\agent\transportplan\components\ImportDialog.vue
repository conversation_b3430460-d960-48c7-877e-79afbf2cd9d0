<template>
  <Dialog :title="'导入国际货运方案'" v-model="dialogVisible" width="500px">
    <el-form ref="formRef" :model="formData" label-width="100px">
      <el-form-item label="Excel文件">
        <el-upload
          ref="uploadRef"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :multiple="false"
          :limit="1"
          :on-exceed="handleExceed"
          :on-success="handleSuccess"
          :on-error="handleError"
          :before-upload="beforeUpload"
          :file-list="fileList"
          accept=".xls,.xlsx"
          :auto-upload="false"
          drag
        >
          <el-icon class="el-icon--upload"><Upload /></el-icon>
          <div class="el-upload__text">拖拽文件到此处，或 <em>点击上传</em></div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 Excel 文件，且不超过 10MB
              <el-link type="primary" :underline="false" @click="downloadTemplate" class="ml-2">
                <Icon icon="ep:download" class="mr-1" />下载模板
              </el-link>
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="更新已有数据" prop="updateSupport">
        <el-switch v-model="formData.updateSupport" />
        <span class="ml-1 text-gray-500 text-sm">开启后，如果数据已存在，将会更新原有数据</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitUpload" :loading="uploadLoading">开始导入</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { Upload } from '@element-plus/icons-vue'
import { getAccessToken, getTenantId } from '@/utils/auth'
import { TransportPlanApi } from '@/api/mall/agent/transportplan'

defineOptions({ name: 'ImportDialog' })

const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const uploadLoading = ref(false) // 上传加载中
const uploadRef = ref() // 上传组件的 Ref
const fileList = ref([]) // 上传的文件列表
const formData = ref({
  updateSupport: false // 是否支持更新已有数据
})

// 上传地址
const uploadUrl = computed(() => {
  const url = import.meta.env.VITE_BASE_URL + '/agent/transport-plan/import-excel'
  // 拼接 updateSupport 参数
  return url + '?updateSupport=' + formData.value.updateSupport
})

// 设置上传的请求头部
const uploadHeaders = computed(() => {
  return {
    Authorization: 'Bearer ' + getAccessToken(),
    'tenant-id': getTenantId()
  }
})

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  // 重置表单
  formData.value = {
    updateSupport: false
  }
  fileList.value = []
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 文件上传前的校验
const beforeUpload = (file) => {
  // 校验文件类型
  const isExcel = file.type === 'application/vnd.ms-excel' || 
                  file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  if (!isExcel) {
    message.error('只能上传 Excel 文件！')
    return false
  }
  
  // 校验文件大小
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB！')
    return false
  }
  
  return true
}

// 文件超出个数限制时的钩子
const handleExceed = () => {
  message.error('最多只能上传1个文件')
}

// 上传成功的回调
const handleSuccess = (response) => {
  uploadLoading.value = false
  if (response.code === 0) {
    message.success('导入成功')
    dialogVisible.value = false
    // 触发成功事件
    emit('success')
  } else {
    message.error(response.msg || '导入失败')
  }
}

// 上传失败的回调
const handleError = () => {
  uploadLoading.value = false
  message.error('导入失败，请检查网络或联系管理员')
}

// 提交上传
const submitUpload = () => {
  if (fileList.value.length === 0) {
    message.warning('请先选择要上传的文件')
    return
  }
  uploadLoading.value = true
  uploadRef.value.submit()
}

// 下载模板
const downloadTemplate = async () => {
  try {
    const data = await TransportPlanApi.downloadTemplate()
    const blob = new Blob([data], { type: 'application/vnd.ms-excel' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '国际货运方案导入模板.xlsx'
    link.click()
    URL.revokeObjectURL(url)
  } catch (error) {
    message.error('下载模板失败')
  }
}

// 定义事件
const emit = defineEmits(['success'])
</script>

<style scoped>
.el-upload__tip {
  display: flex;
  align-items: center;
  line-height: 1.5;
}

.el-icon--upload {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 10px;
}

.el-upload__text {
  margin-bottom: 20px;
}

.el-upload__text em {
  color: #409EFF;
  font-style: normal;
}
</style>
