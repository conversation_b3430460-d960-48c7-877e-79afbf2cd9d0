<template>
  <Dialog :title="'咨询详情'" v-model="dialogVisible" width="700px">
    <el-descriptions :column="1" border>
      <el-descriptions-item label="用户名">{{ detailData.name }}</el-descriptions-item>
      <el-descriptions-item label="邮箱">{{ detailData.email }}</el-descriptions-item>
      <el-descriptions-item label="电话">{{ detailData.phone }}</el-descriptions-item>
      <el-descriptions-item label="公司名称">{{ detailData.companyName }}</el-descriptions-item>
      <el-descriptions-item label="咨询类型">
        <dict-tag :type="DICT_TYPE.MEMBER_INQUIRY_TYPE" :value="detailData.type" />
      </el-descriptions-item>
      <el-descriptions-item label="描述">
        <div class="whitespace-pre-wrap">{{ detailData.description }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        <div class="whitespace-pre-wrap">{{ detailData.remark }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <dict-tag :type="DICT_TYPE.MEMBER_INQUIRY_STATUS" :value="detailData.status" />
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { InquiryApi, InquiryVO } from '@/api/member/inquiry'
import { formatDate } from '@/utils/formatTime'

/** 用户咨询详情 */
defineOptions({ name: 'InquiryDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 详情的加载中
const detailData = ref<InquiryVO>({} as InquiryVO) // 详情数据

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    detailData.value = await InquiryApi.getInquiry(id)
  } finally {
    detailLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
</style>
