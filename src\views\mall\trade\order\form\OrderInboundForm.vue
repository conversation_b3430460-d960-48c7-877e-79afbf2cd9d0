<template>
  <Dialog v-model="dialogVisible" title="订单入库" width="80%">
    <div v-loading="formLoading">
      <!-- 订单信息 -->
      <div class="mb-4 p-4 bg-gray-50 rounded">
        <div class="text-lg font-medium mb-2">订单信息</div>
        <div class="grid grid-cols-3 gap-4 text-sm">
          <div>订单号：{{ orderData?.no }}</div>
          <div>订单状态：{{ orderData?.status }}</div>
          <div>商品数量：{{ orderData?.productCount }}</div>
        </div>
      </div>

      <!-- 商品入库信息 -->
      <div class="text-lg font-medium mb-4">商品入库信息</div>
      
      <el-form ref="formRef" :model="formData" label-width="120px">
        <div v-for="(item, index) in formData.items" :key="item.itemId" class="border rounded p-4 mb-4">
          <!-- 商品信息展示 -->
          <div class="flex items-center mb-4 pb-3 border-b">
            <el-image
              v-if="getOrderItem(item.itemId)?.picUrl"
              :src="getOrderItem(item.itemId)?.picUrl"
              class="!h-[60px] !w-[60px] mr-3"
              fit="contain"
            />
            <div class="flex-1">
              <div class="font-medium text-base">{{ getOrderItem(item.itemId)?.spuName }}</div>
              <div class="text-sm text-gray-500 mt-1">
                <span class="mr-4">订单数量：{{ getOrderItem(item.itemId)?.count }}</span>
                <span>实付金额：¥{{ ((getOrderItem(item.itemId)?.payPrice || 0) / 100).toFixed(2) }}</span>
              </div>
            </div>
            <div class="text-right">
              <span class="text-lg font-medium text-blue-600">商品 {{ index + 1 }}</span>
            </div>
          </div>

          <!-- 入库信息表单 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item 
                :label="`仓库`" 
                :prop="`items.${index}.warehouseId`"
                :rules="[{ required: true, message: '请选择仓库', trigger: 'change' }]"
              >
                <el-select v-model="item.warehouseId" placeholder="请选择仓库" style="width: 100%">
                  <el-option
                    v-for="warehouse in props.warehouseList"
                    :key="warehouse.id"
                    :value="warehouse.id"
                    :label="warehouse.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item 
                :label="`库位`" 
                :prop="`items.${index}.location`"
                :rules="[{ required: true, message: '请输入库位', trigger: 'blur' }]"
              >
                <el-input v-model="item.location" placeholder="请输入库位" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item 
                :label="`入库数量`" 
                :prop="`items.${index}.count`"
                :rules="[{ required: true, message: '请输入入库数量', trigger: 'blur' }]"
              >
                <el-input-number 
                  v-model="item.count" 
                  :min="1" 
                  :max="getOrderItem(item.itemId)?.count || 999"
                  placeholder="请输入入库数量" 
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item 
                :label="`重量(g)`" 
                :prop="`items.${index}.weight`"
                :rules="[{ required: true, message: '请输入重量', trigger: 'blur' }]"
              >
                <el-input-number 
                  v-model="item.weight" 
                  :min="0" 
                  placeholder="请输入重量" 
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item 
                :label="`长度(cm)`" 
                :prop="`items.${index}.length`"
                :rules="[{ required: true, message: '请输入长度', trigger: 'blur' }]"
              >
                <el-input-number 
                  v-model="item.length" 
                  :min="0" 
                  :precision="2"
                  placeholder="请输入长度" 
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item 
                :label="`宽度(cm)`" 
                :prop="`items.${index}.width`"
                :rules="[{ required: true, message: '请输入宽度', trigger: 'blur' }]"
              >
                <el-input-number 
                  v-model="item.width" 
                  :min="0" 
                  :precision="2"
                  placeholder="请输入宽度" 
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item 
                :label="`高度(cm)`" 
                :prop="`items.${index}.height`"
                :rules="[{ required: true, message: '请输入高度', trigger: 'blur' }]"
              >
                <el-input-number 
                  v-model="item.height" 
                  :min="0" 
                  :precision="2"
                  placeholder="请输入高度" 
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="`质检图片`">
                <UploadImgs 
                  v-model="item.inspectPicUrls" 
                  :limit="10"
                  :is-show-tip="true"
                />
                <div class="text-xs text-gray-500 mt-1">最多上传10张图片</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="`质检视频`">
                <UploadImgs 
                  v-model="item.inspectVideoUrls" 
                  :limit="6"
                  :is-show-tip="true"
                  file-type="video"
                />
                <div class="text-xs text-gray-500 mt-1">最多上传6个视频</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="`相关文件`">
                <UploadFile v-model="item.fileUrl" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="`备注`">
                <el-input 
                  v-model="item.remark" 
                  type="textarea" 
                  :rows="2"
                  placeholder="请输入备注信息"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import * as TradeOrderApi from '@/api/mall/trade/order'

defineOptions({ name: 'OrderInboundForm' })

// 定义props
interface Props {
  warehouseList?: Array<{
    id: number
    name: string
  }>
}

const props = withDefaults(defineProps<Props>(), {
  warehouseList: () => []
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const orderData = ref<TradeOrderApi.OrderVO>() // 当前订单信息

const formData = ref<TradeOrderApi.BatchInboundVO>({
  items: []
})

const formRef = ref() // 表单 Ref

// 根据订单项ID获取订单项信息
const getOrderItem = (itemId: number) => {
  return orderData.value?.items?.find(item => item.id === itemId)
}

/** 打开弹窗 */
const open = async (row: TradeOrderApi.OrderVO) => {
  resetForm()
  orderData.value = row
  
  // 初始化每个订单项的入库信息
  if (row.items && row.items.length > 0) {
    formData.value.items = row.items.map(item => ({
      itemId: item.id || 0,
      warehouseId: 0,
      location: '',
      count: item.count || 1,
      weight: 0,
      length: 0,
      width: 0,
      height: 0,
      inspectPicUrls: [],
      inspectVideoUrls: [],
      fileUrl: '',
      remark: ''
    }))
  }
  
  dialogVisible.value = true
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  
  // 提交请求
  formLoading.value = true
  try {
    await TradeOrderApi.inboundOrderItem(formData.value)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success', true)
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    items: []
  }
  orderData.value = undefined
  formRef.value?.resetFields()
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 16px;
}
</style>
