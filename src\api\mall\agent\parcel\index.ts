import request from '@/config/axios'

// 代购包裹 VO
export interface ParcelVO {
  id: number // 编号
  no: string // 流水号
  userId: number // 用户编号
  status: number // 包裹状态
  productCount: number // 包裹商品数量
  cancelType: number // 取消类型
  remark: string // 平台备注
  commentStatus: boolean // 是否评价
  brokerageUserId: number // 推广人编号
  payOrderId: number // 支付订单编号
  payOrderIds: string // 支付订单编号集合
  payStatus: boolean // 是否支付
  payTime: Date // 订单支付时间
  payChannelCode: string // 支付成功的支付渠道
  finishTime: Date // 包裹完成时间
  cancelTime: Date // 包裹取消时间
  totalPrice: number // 订单原价
  discountPrice: number // 订单优惠
  deliveryPrice: number // 运费金额
  insurancePrice: number // 保险金额
  servicePrice: number // 服务金额
  adjustPrice: number // 订单调价
  payPrice: number // 应付金额
  weight: number // 商品重量
  volume: number // 商品体积
  length: number // 长
  width: number // 宽
  height: number // 高
  packingWeight: number // 包装重量
  deliveryType: number // 配送类型
  transportPlanId: number // 物流方案编号
  transportCompanyId: number // 物流公司编号
  transportNo: string // 物流单号
  transportTrack: string // 物流跟踪
  declareContent: string // 海关申报内容
  declareValue: number // 海关申报价值
  clearanceCode: string // 清关代码
  insuranceServices: string // 保险服务数组
  freeServices: string // 免费服务数组
  chargeServices: string // 收费服务数组
  deliveryTime: Date // 发货时间
  receiveTime: Date // 收货时间
  receiverName: string // 收件人名称
  receiverMobile: string // 收件人手机
  receiverAreaId: number // 收件人地区编号
  receiverDetailAddress: string // 收件人详细地址
  receiverPhoneCode: string // 收件人手机
  receiverPostCode: string // 邮编
  receiverCountryCode: string // 国家编码
  refundStatus: number // 售后状态
  refundPrice: number // 退款金额
  couponId: number // 优惠劵编号
  couponPrice: number // 优惠劵减免金额
  usePoint: number // 使用的积分
  pointPrice: number // 积分抵扣的金额
  givePoint: number // 赠送的积分
  vipPrice: number // VIP 减免金额
  giveCouponTemplateCounts: string // 赠送的优惠劵
  giveCouponIds: string // 赠送的优惠劵编号
}

// 代购包裹 API
export const ParcelApi = {
  // 查询代购包裹分页
  getParcelPage: async (params: any) => {
    return await request.get({ url: `/agent/parcel/page`, params })
  },

  // 查询代购包裹详情
  getParcel: async (id: number) => {
    return await request.get({ url: `/agent/parcel/get?id=` + id })
  },

  // 新增代购包裹
  createParcel: async (data: ParcelVO) => {
    return await request.post({ url: `/agent/parcel/create`, data })
  },

  // 修改代购包裹
  updateParcel: async (data: ParcelVO) => {
    return await request.put({ url: `/agent/parcel/update`, data })
  },

  // 删除代购包裹
  deleteParcel: async (id: number) => {
    return await request.delete({ url: `/agent/parcel/delete?id=` + id })
  },

  // 导出代购包裹 Excel
  exportParcel: async (params) => {
    return await request.download({ url: `/agent/parcel/export-excel`, params })
  },

// ==================== 子表（代购包裹明细） ====================

  // 获得代购包裹明细列表
  getParcelItemListByParcelId: async (parcelId) => {
    return await request.get({ url: `/agent/parcel/parcel-item/list-by-parcel-id?parcelId=` + parcelId })
  }
}