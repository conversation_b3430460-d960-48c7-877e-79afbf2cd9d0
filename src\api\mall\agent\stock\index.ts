import request from '@/config/axios'

// 代购仓库 VO
export interface StockVO {
  id: number // 品牌编号
  warehouseId: number // 仓库编号
  userId: number // 用户编号
  spuId: number // spu编号
  spuName: string // 商品 SPU 名称
  skuId: number // 商品 SKU 编号
  properties: string // 商品属性数组，JSON 格式
  picUrl: string // 商品图片
  count: number // 库存数量
  weight: number // 商品重量
  volume: number // 商品体积
  inspectPicUrls: string // 入库质检图片
  fileUrl: string // 相关文件地址
  remark: string // 备注
  status: number // 状态
  inTime: Date // 入库日期
  expiredTime: Date // 到期日期
}

// 代购仓库 API
export const StockApi = {
  // 查询代购仓库分页
  getStockPage: async (params: any) => {
    return await request.get({ url: `/agent/stock/page`, params })
  },

  // 查询代购仓库详情
  getStock: async (id: number) => {
    return await request.get({ url: `/agent/stock/get?id=` + id })
  },

  // 新增代购仓库
  createStock: async (data: StockVO) => {
    return await request.post({ url: `/agent/stock/create`, data })
  },

  // 修改代购仓库
  updateStock: async (data: StockVO) => {
    return await request.put({ url: `/agent/stock/update`, data })
  },

  // 删除代购仓库
  deleteStock: async (id: number) => {
    return await request.delete({ url: `/agent/stock/delete?id=` + id })
  },

  // 导出代购仓库 Excel
  exportStock: async (params) => {
    return await request.download({ url: `/agent/stock/export-excel`, params })
  }
}