<template>
  <Dialog v-model="dialogVisible" title="订单项入库" width="60%">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="120px">
      <!-- 商品信息展示 -->
      <el-form-item label="商品信息">
        <div class="flex items-center">
          <el-image
            v-if="orderItem?.picUrl"
            :src="orderItem.picUrl"
            class="!h-[60px] !w-[60px] mr-3"
            fit="contain"
          />
          <div>
            <div class="font-medium text-base">{{ orderItem?.spuName }}</div>
            <div class="text-sm text-gray-500 mt-1">
              <span class="mr-4">订单数量：{{ orderItem?.count }}</span>
              <span>实付金额：¥{{ (orderItem?.payPrice || 0) / 100 }}</span>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="仓库" prop="warehouseId">
            <el-select v-model="formData.warehouseId" placeholder="请选择仓库" style="width: 100%">
              <el-option
                v-for="item in props.warehouseList"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="库位" prop="location">
            <el-input v-model="formData.location" placeholder="请输入库位" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="入库数量" prop="count">
            <el-input-number 
              v-model="formData.count" 
              :min="1" 
              :max="orderItem?.count || 999"
              placeholder="请输入入库数量" 
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重量(g)" prop="weight">
            <el-input-number 
              v-model="formData.weight" 
              :min="0" 
              placeholder="请输入重量" 
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="长度(cm)" prop="length">
            <el-input-number 
              v-model="formData.length" 
              :min="0" 
              :precision="2"
              placeholder="请输入长度" 
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="宽度(cm)" prop="width">
            <el-input-number 
              v-model="formData.width" 
              :min="0" 
              :precision="2"
              placeholder="请输入宽度" 
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="高度(cm)" prop="height">
            <el-input-number 
              v-model="formData.height" 
              :min="0" 
              :precision="2"
              placeholder="请输入高度" 
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="质检图片" prop="inspectPicUrls">
        <UploadImgs 
          v-model="formData.inspectPicUrls" 
          :limit="10"
          :is-show-tip="true"
        />
        <div class="text-xs text-gray-500 mt-1">最多上传10张图片</div>
      </el-form-item>

      <el-form-item label="质检视频" prop="inspectVideoUrls">
        <UploadImgs 
          v-model="formData.inspectVideoUrls" 
          :limit="6"
          :is-show-tip="true"
          file-type="video"
        />
        <div class="text-xs text-gray-500 mt-1">最多上传6个视频</div>
      </el-form-item>

      <el-form-item label="相关文件" prop="fileUrl">
        <UploadFile v-model="formData.fileUrl" />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input 
          v-model="formData.remark" 
          type="textarea" 
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import * as TradeOrderApi from '@/api/mall/trade/order'

defineOptions({ name: 'OrderItemInboundForm' })

// 定义props
interface Props {
  warehouseList?: Array<{
    id: number
    name: string
  }>
}

const props = withDefaults(defineProps<Props>(), {
  warehouseList: () => []
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const orderItem = ref<TradeOrderApi.OrderItemRespVO>() // 当前订单项信息

const formData = ref<TradeOrderApi.InboundItemVO>({
  itemId: 0,
  warehouseId: 0,
  location: '',
  count: 1,
  weight: 0,
  length: 0,
  width: 0,
  height: 0,
  inspectPicUrls: [],
  inspectVideoUrls: [],
  fileUrl: '',
  remark: ''
})

const formRules = reactive({
  warehouseId: [{ required: true, message: '请选择仓库', trigger: 'change' }],
  location: [{ required: true, message: '请输入库位', trigger: 'blur' }],
  count: [{ required: true, message: '请输入入库数量', trigger: 'blur' }],
  weight: [{ required: true, message: '请输入重量', trigger: 'blur' }],
  length: [{ required: true, message: '请输入长度', trigger: 'blur' }],
  width: [{ required: true, message: '请输入宽度', trigger: 'blur' }],
  height: [{ required: true, message: '请输入高度', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (row: TradeOrderApi.OrderItemRespVO) => {
  resetForm()
  orderItem.value = row
  formData.value.itemId = row.id || 0
  formData.value.count = row.count || 1
  dialogVisible.value = true
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  
  // 提交请求
  formLoading.value = true
  try {
    const batchData: TradeOrderApi.BatchInboundVO = {
      items: [formData.value]
    }
    
    await TradeOrderApi.inboundOrderItem(batchData)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success', true)
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    itemId: 0,
    warehouseId: 0,
    location: '',
    count: 1,
    weight: 0,
    length: 0,
    width: 0,
    height: 0,
    inspectPicUrls: [],
    inspectVideoUrls: [],
    fileUrl: '',
    remark: ''
  }
  orderItem.value = undefined
  formRef.value?.resetFields()
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
}
</style>
