<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="服务编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入服务编码" />
      </el-form-item>
      <el-form-item label="服务图标" prop="icon">
        <el-input v-model="formData.icon" placeholder="请输入服务图标" />
      </el-form-item>
      <el-form-item label="服务类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择类型" class="w-full">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.AGENT_SERVE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否免费" prop="free">
        <el-radio-group v-model="formData.free">
          <el-radio
            v-for="dict in getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value as string"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="收费金额" prop="price">
        <el-input v-model="formData.price" placeholder="请输入收费金额，单位：分" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="formData.sort"
          :min="0"
          :precision="0"
          :step="1"
          placeholder="请输入排序"
        />
      </el-form-item>

      <!-- 多语言名称 -->
      <el-form-item label="中文名称" prop="nameZh">
        <el-input v-model="formData.nameZh" placeholder="请输入中文名称" />
      </el-form-item>
      <el-form-item label="英文名称" prop="nameEn">
        <el-input v-model="formData.nameEn" placeholder="请输入英文名称" />
      </el-form-item>
      <el-form-item label="法语名称" prop="nameFr">
        <el-input v-model="formData.nameFr" placeholder="请输入法语名称" />
      </el-form-item>
      <el-form-item label="德语名称" prop="nameDe">
        <el-input v-model="formData.nameDe" placeholder="请输入德语名称" />
      </el-form-item>
      <el-form-item label="西班牙语名称" prop="nameEs">
        <el-input v-model="formData.nameEs" placeholder="请输入西班牙语名称" />
      </el-form-item>
      <el-form-item label="阿拉伯语名称" prop="nameAr">
        <el-input v-model="formData.nameAr" placeholder="请输入阿拉伯语名称" />
      </el-form-item>

      <!-- 多语言描述 -->
      <el-form-item label="中文描述" prop="descriptionZh" class="full-width">
        <el-input
          v-model="formData.descriptionZh"
          placeholder="请输入中文描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="英文描述" prop="descriptionEn" class="full-width">
        <el-input
          v-model="formData.descriptionEn"
          placeholder="请输入英文描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="法语描述" prop="descriptionFr" class="full-width">
        <el-input
          v-model="formData.descriptionFr"
          placeholder="请输入法语描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="德语描述" prop="descriptionDe" class="full-width">
        <el-input
          v-model="formData.descriptionDe"
          placeholder="请输入德语描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="西班牙语描述" prop="descriptionEs" class="full-width">
        <el-input
          v-model="formData.descriptionEs"
          placeholder="请输入西班牙语描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="阿拉伯语描述" prop="descriptionAr" class="full-width">
        <el-input
          v-model="formData.descriptionAr"
          placeholder="请输入阿拉伯语描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getBoolDictOptions, DICT_TYPE } from '@/utils/dict'
import { ServeApi, ServeVO } from '@/api/mall/agent/serve'

/** 代购服务项目 表单 */
defineOptions({ name: 'ServeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  icon: undefined,
  type: undefined,
  nameZh: undefined,
  nameEn: undefined,
  nameFr: undefined,
  nameDe: undefined,
  nameEs: undefined,
  nameAr: undefined,
  descriptionZh: undefined,
  descriptionEn: undefined,
  descriptionFr: undefined,
  descriptionDe: undefined,
  descriptionEs: undefined,
  descriptionAr: undefined,
  free: 'false',
  price: undefined,
  sort: 0,
  status: 0 // 默认开启
})
const formRules = reactive({
  code: [{ required: true, message: '服务编码不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型;保险，免费服务，收费服务不能为空', trigger: 'change' }],
  nameZh: [{ required: true, message: '中文名称不能为空', trigger: 'blur' }],
  nameEn: [{ required: true, message: '英文名称不能为空', trigger: 'blur' }],
  free: [{ required: true, message: '是否免费不能为空', trigger: 'blur' }],
  price: [{ required: true, message: '收费金额，单位使用：分不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '开启状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ServeApi.getServe(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ServeVO
    if (formType.value === 'create') {
      await ServeApi.createServe(data)
      message.success(t('common.createSuccess'))
    } else {
      await ServeApi.updateServe(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    icon: undefined,
    type: undefined,
    nameZh: undefined,
    nameEn: undefined,
    nameFr: undefined,
    nameDe: undefined,
    nameEs: undefined,
    nameAr: undefined,
    descriptionZh: undefined,
    descriptionEn: undefined,
    descriptionFr: undefined,
    descriptionDe: undefined,
    descriptionEs: undefined,
    descriptionAr: undefined,
    free: 'false',
    price: undefined,
    sort: 0,
    status: 0 // 默认开启
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.form-container {
  display: flex;
  flex-wrap: wrap;
}

.form-container :deep(.el-form-item) {
  width: 48%;
  margin-right: 2%;
}

.form-container :deep(.full-width) {
  width: 100%;
}

.form-container :deep(.el-textarea__inner) {
  width: 100%;
}

.w-full {
  width: 100%;
}
</style>
