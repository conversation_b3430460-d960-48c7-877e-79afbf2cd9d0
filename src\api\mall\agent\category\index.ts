import request from '@/config/axios'

// 代购商品分类 VO
export interface CategoryVO {
  id: number // 分类编号
  name: string // 分类名称
  picUrl: string // 分类图
  sort: number // 分类排序
  status: number // 开启状态
}

// 代购商品分类 API
export const CategoryApi = {
  // 查询代购商品分类分页
  getCategoryPage: async (params: any) => {
    return await request.get({ url: `/agent/category/page`, params })
  },

  // 查询代购商品分类详情
  getCategory: async (id: number) => {
    return await request.get({ url: `/agent/category/get?id=` + id })
  },

  // 新增代购商品分类
  createCategory: async (data: CategoryVO) => {
    return await request.post({ url: `/agent/category/create`, data })
  },

  // 修改代购商品分类
  updateCategory: async (data: CategoryVO) => {
    return await request.put({ url: `/agent/category/update`, data })
  },

  // 删除代购商品分类
  deleteCategory: async (id: number) => {
    return await request.delete({ url: `/agent/category/delete?id=` + id })
  },

  // 导出代购商品分类 Excel
  exportCategory: async (params) => {
    return await request.download({ url: `/agent/category/export-excel`, params })
  }
}