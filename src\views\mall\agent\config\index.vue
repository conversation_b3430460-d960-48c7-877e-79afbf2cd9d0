<template>
  <ContentWrap>
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item v-show="false" label="hideId">
        <el-input v-model="formData.id" />
      </el-form-item>
      <el-tabs>
        <!-- 售后 -->
        <el-tab-pane label="售后">
          <el-form-item label="退款理由" prop="afterSaleRefundReasons">
            <el-select
              v-model="formData.afterSaleRefundReasons"
              allow-create
              filterable
              multiple
              placeholder="请直接输入退款理由"
            >
              <el-option
                v-for="reason in formData.afterSaleRefundReasons"
                :key="reason"
                :label="reason"
                :value="reason"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="退货理由" prop="afterSaleReturnReasons">
            <el-select
              v-model="formData.afterSaleReturnReasons"
              allow-create
              filterable
              multiple
              placeholder="请直接输入退货理由"
            >
              <el-option
                v-for="reason in formData.afterSaleReturnReasons"
                :key="reason"
                :label="reason"
                :value="reason"
              />
            </el-select>
          </el-form-item>
        </el-tab-pane>
        <!-- 配送 -->
        <el-tab-pane label="配送">
          <el-form-item label="启用包邮" prop="deliveryExpressFreeEnabled">
            <el-switch v-model="formData.deliveryExpressFreeEnabled" style="user-select: none" />
            <el-text class="w-full" size="small" type="info"> 商城是否启用全场包邮</el-text>
          </el-form-item>
          <el-form-item label="满额包邮" prop="deliveryExpressFreePrice">
            <el-input-number
              v-model="formData.deliveryExpressFreePrice"
              :min="0"
              :precision="2"
              class="!w-xs"
              placeholder="请输入满额包邮"
            />
            <el-text class="w-full" size="small" type="info">
              商城商品满多少金额即可包邮，单位：元
            </el-text>
          </el-form-item>
        </el-tab-pane>
        <!-- 分销 -->
        <el-tab-pane label="分销">
          <el-form-item label="分佣启用" prop="brokerageEnabled">
            <el-switch v-model="formData.brokerageEnabled" style="user-select: none" />
            <el-text class="w-full" size="small" type="info"> 商城是否开启分销模式</el-text>
          </el-form-item>
          <el-form-item label="分佣模式" prop="brokerageEnabledCondition">
            <el-radio-group v-model="formData.brokerageEnabledCondition">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.BROKERAGE_ENABLED_CONDITION)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
            <el-text class="w-full" size="small" type="info">
              人人分销：每个用户都可以成为推广员
            </el-text>
            <el-text class="w-full" size="small" type="info">
              指定分销：仅可在后台手动设置推广员
            </el-text>
          </el-form-item>
          <el-form-item label="分销关系绑定" prop="brokerageBindMode">
            <el-radio-group v-model="formData.brokerageBindMode">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.BROKERAGE_BIND_MODE)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
            <el-text class="w-full" size="small" type="info">
              首次绑定：只要用户没有推广人，随时都可以绑定推广关系
            </el-text>
            <el-text class="w-full" size="small" type="info">
              注册绑定：只有新用户注册时或首次进入系统时才可以绑定推广关系
            </el-text>
          </el-form-item>
          <el-form-item label="分销海报图">
            <UploadImgs v-model="formData.brokeragePosterUrls" height="125px" width="75px" />
            <el-text class="w-full" size="small" type="info">
              个人中心分销海报图片，建议尺寸 600x1000
            </el-text>
          </el-form-item>
          <el-form-item label="一级返佣比例" prop="brokerageFirstPercent">
            <el-input-number
              v-model="formData.brokerageFirstPercent"
              :max="100"
              :min="0"
              class="!w-xs"
              placeholder="请输入一级返佣比例"
            />
            <el-text class="w-full" size="small" type="info">
              订单交易成功后给推广人返佣的百分比
            </el-text>
          </el-form-item>
          <el-form-item label="二级返佣比例" prop="brokerageSecondPercent">
            <el-input-number
              v-model="formData.brokerageSecondPercent"
              :max="100"
              :min="0"
              class="!w-xs"
              placeholder="请输入二级返佣比例"
            />
            <el-text class="w-full" size="small" type="info">
              订单交易成功后给推广人的推荐人返佣的百分比
            </el-text>
          </el-form-item>
          <el-form-item label="佣金冻结天数" prop="brokerageFrozenDays">
            <el-input-number
              v-model="formData.brokerageFrozenDays"
              :min="0"
              class="!w-xs"
              placeholder="请输入佣金冻结天数"
            />
            <el-text class="w-full" size="small" type="info">
              防止用户退款，佣金被提现了，所以需要设置佣金冻结时间，单位：天
            </el-text>
          </el-form-item>
          <el-form-item label="提现最低金额" prop="brokerageWithdrawMinPrice">
            <el-input-number
              v-model="formData.brokerageWithdrawMinPrice"
              :min="0"
              :precision="2"
              class="!w-xs"
              placeholder="请输入提现最低金额"
            />
            <el-text class="w-full" size="small" type="info">
              用户提现最低金额限制，单位：元
            </el-text>
          </el-form-item>
          <el-form-item label="提现手续费" prop="brokerageWithdrawFeePercent">
            <el-input-number
              v-model="formData.brokerageWithdrawFeePercent"
              :max="100"
              :min="0"
              class="!w-xs"
              placeholder="请输入提现手续费"
            />
            <el-text class="w-full" size="small" type="info">
              提现手续费百分比，范围 0-100，0 为无提现手续费。例：设置 10，即收取 10% 手续费，提现
              10 元，到账 9 元，1 元手续费
            </el-text>
          </el-form-item>
          <el-form-item label="提现方式" prop="brokerageWithdrawTypes">
            <el-checkbox-group v-model="formData.brokerageWithdrawTypes">
              <el-checkbox
                v-for="dict in getIntDictOptions(DICT_TYPE.BROKERAGE_WITHDRAW_TYPE)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-checkbox>
            </el-checkbox-group>
            <el-text class="w-full" size="small" type="info"> 商城开通提现的付款方式</el-text>
          </el-form-item>
        </el-tab-pane>
        <!-- 平台 -->
        <el-tab-pane label="平台">
          <el-form-item label="平台佣金模式" prop="platformFeeType">
            <el-radio-group v-model="formData.platformFeeType">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.TRADE_PLATFORM_FEE_TYPE)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="平台佣金值" prop="platformFeeValue">
            <el-input-number
              v-model="formData.platformFeeValue"
              :min="0"
              class="!w-xs"
              placeholder="请输入平台佣金值"
            />
            <el-text class="w-full" size="small" type="info">
              金额单位为分，百分比请输入整数值
            </el-text>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
      <!-- 保存 -->
      <el-form-item>
        <el-button :loading="formLoading" type="primary" @click="submitForm"> 保存</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
</template>

<script lang="ts" setup>
import * as ConfigApi from '@/api/mall/agent/config'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { cloneDeep } from 'lodash-es'

defineOptions({ name: 'AgentConfig' })

const message = useMessage() // 消息弹窗

const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formRef = ref()
const formData = ref({
  id: null,
  afterSaleRefundReasons: [],
  afterSaleReturnReasons: [],
  deliveryExpressFreeEnabled: false,
  deliveryExpressFreePrice: 0,
  deliveryPickUpEnabled: false,
  brokerageEnabled: false,
  brokerageEnabledCondition: undefined,
  brokerageBindMode: undefined,
  brokeragePosterUrls: [],
  brokerageFirstPercent: 0,
  brokerageSecondPercent: 0,
  brokerageWithdrawMinPrice: 0,
  brokerageWithdrawFeePercent: 0,
  brokerageFrozenDays: 0,
  brokerageWithdrawTypes: [],
  platformFeeType: 1,
  platformFeeValue: 0
})
// 定义验证器类型
type ValidatorCallback = (error?: Error) => void
type ValidatorRule = {
  required: boolean
  message: string
  trigger: string
  validator?: (rule: any, value: any, callback: ValidatorCallback) => void
}

const formRules = reactive({
  deliveryExpressFreePrice: [{ required: true, message: '满额包邮不能为空', trigger: 'blur' }],
  brokerageEnabledCondition: [
    {
      required: true,
      message: '分佣模式不能为空',
      trigger: 'blur',
      validator: (_: any, value: any, callback: ValidatorCallback) => {
        if (!formData.value.brokerageEnabled) {
          callback()
          return
        }
        if (!value && value !== 0) {
          callback(new Error('分佣模式不能为空'))
          return
        }
        callback()
      }
    }
  ],
  brokerageBindMode: [
    {
      required: true,
      message: '分销关系绑定模式不能为空',
      trigger: 'blur',
      validator: (_: any, value: any, callback: ValidatorCallback) => {
        if (!formData.value.brokerageEnabled) {
          callback()
          return
        }
        if (!value && value !== 0) {
          callback(new Error('分销关系绑定模式不能为空'))
          return
        }
        callback()
      }
    }
  ],
  brokerageFirstPercent: [
    {
      required: true,
      message: '一级返佣比例不能为空',
      trigger: 'blur',
      validator: (_: any, value: any, callback: ValidatorCallback) => {
        if (!formData.value.brokerageEnabled) {
          callback()
          return
        }
        if (value === undefined || value === null) {
          callback(new Error('一级返佣比例不能为空'))
          return
        }
        callback()
      }
    }
  ],
  brokerageSecondPercent: [
    {
      required: true,
      message: '二级返佣比例不能为空',
      trigger: 'blur',
      validator: (_: any, value: any, callback: ValidatorCallback) => {
        if (!formData.value.brokerageEnabled) {
          callback()
          return
        }
        if (value === undefined || value === null) {
          callback(new Error('二级返佣比例不能为空'))
          return
        }
        callback()
      }
    }
  ],
  brokerageWithdrawMinPrice: [
    {
      required: true,
      message: '用户提现最低金额不能为空',
      trigger: 'blur',
      validator: (_: any, value: any, callback: ValidatorCallback) => {
        if (!formData.value.brokerageEnabled) {
          callback()
          return
        }
        if (value === undefined || value === null) {
          callback(new Error('用户提现最低金额不能为空'))
          return
        }
        callback()
      }
    }
  ],
  brokerageWithdrawFeePercent: [
    {
      required: true,
      message: '提现手续费不能为空',
      trigger: 'blur',
      validator: (_: any, value: any, callback: ValidatorCallback) => {
        if (!formData.value.brokerageEnabled) {
          callback()
          return
        }
        if (value === undefined || value === null) {
          callback(new Error('提现手续费不能为空'))
          return
        }
        callback()
      }
    }
  ],
  brokerageFrozenDays: [
    {
      required: true,
      message: '佣金冻结时间不能为空',
      trigger: 'blur',
      validator: (_: any, value: any, callback: ValidatorCallback) => {
        if (!formData.value.brokerageEnabled) {
          callback()
          return
        }
        if (value === undefined || value === null) {
          callback(new Error('佣金冻结时间不能为空'))
          return
        }
        callback()
      }
    }
  ],
  brokerageWithdrawTypes: [
    {
      required: true,
      message: '提现方式不能为空',
      trigger: 'change',
      validator: (_: any, value: any, callback: ValidatorCallback) => {
        if (!formData.value.brokerageEnabled) {
          callback()
          return
        }
        if (!value || value.length === 0) {
          callback(new Error('提现方式不能为空'))
          return
        }
        callback()
      }
    }
  ]
})

const submitForm = async () => {
  if (formLoading.value) return
  // 校验表单
  if (!formRef) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 提交请求
    formLoading.value = true

    // 克隆数据，避免修改原始数据
    const data = cloneDeep(unref(formData.value)) as unknown as ConfigApi.ConfigVO

    // 如果分销功能未启用，设置默认值
    if (!data.brokerageEnabled) {
      data.brokerageEnabledCondition = data.brokerageEnabledCondition || 0
      data.brokerageBindMode = data.brokerageBindMode || 0
      data.brokerageFirstPercent = data.brokerageFirstPercent || 0
      data.brokerageSecondPercent = data.brokerageSecondPercent || 0
      data.brokerageFrozenDays = data.brokerageFrozenDays || 0
      data.brokerageWithdrawMinPrice = data.brokerageWithdrawMinPrice || 0
      data.brokerageWithdrawFeePercent = data.brokerageWithdrawFeePercent || 0
      data.brokerageWithdrawTypes = data.brokerageWithdrawTypes?.length
        ? data.brokerageWithdrawTypes
        : [0]
    }

    // 金额放大
    data.deliveryExpressFreePrice = data.deliveryExpressFreePrice * 100
    data.brokerageWithdrawMinPrice = data.brokerageWithdrawMinPrice * 100

    await ConfigApi.saveTradeConfig(data)
    message.success('保存成功')
  } catch (error) {
    console.error('提交表单出错:', error)
    message.error('保存失败，请检查表单数据')
  } finally {
    formLoading.value = false
  }
}

/** 查询交易中心配置 */
const getConfig = async () => {
  formLoading.value = true
  try {
    const data = await ConfigApi.getTradeConfig()
    if (data != null) {
      formData.value = data
      // 金额缩小
      formData.value.deliveryExpressFreePrice = data.deliveryExpressFreePrice / 100
      formData.value.brokerageWithdrawMinPrice = data.brokerageWithdrawMinPrice / 100
    }
  } finally {
    formLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getConfig()
})
</script>
