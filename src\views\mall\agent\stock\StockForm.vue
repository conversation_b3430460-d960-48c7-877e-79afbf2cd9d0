<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="仓库编号" prop="warehouseId">
        <el-select v-model="formData.warehouseId" placeholder="请选择仓库编号" style="width: 100%">
          <el-option
            v-for="item in warehouseList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="库位" prop="warehouseId">
        <el-input v-model="formData.location" placeholder="请输入仓库编号" />
      </el-form-item>
      <el-form-item label="用户编号" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户编号" />
      </el-form-item>
      <el-form-item label="SPU编号" prop="spuId">
        <el-input v-model="formData.spuId" placeholder="请输入spu编号" />
      </el-form-item>
      <el-form-item label="商品名称" prop="spuName">
        <el-input v-model="formData.spuName" placeholder="请输入商品 SPU 名称" />
      </el-form-item>
      <el-form-item label="SKU编号" prop="skuId">
        <el-input v-model="formData.skuId" placeholder="请输入商品 SKU 编号" />
      </el-form-item>
      <el-form-item label="商品属性" prop="properties">
        <el-input v-model="formData.properties" placeholder="请输入商品属性" />
      </el-form-item>
      <el-form-item label="商品图片" prop="picUrl">
        <el-input v-model="formData.picUrl" placeholder="请输入商品图片" />
      </el-form-item>
      <el-form-item label="库存数量" prop="count">
        <el-input v-model="formData.count" placeholder="请输入库存数量" />
      </el-form-item>
      <el-form-item label="商品重量" prop="weight">
        <el-input v-model="formData.weight" placeholder="请输入商品重量" />
      </el-form-item>
      <el-form-item label="长度" prop="length">
        <el-input v-model="formData.length" placeholder="请输入商品长度" />
      </el-form-item>
      <el-form-item label="宽度" prop="width">
        <el-input v-model="formData.width" placeholder="请输入商品宽度" />
      </el-form-item>
      <el-form-item label="高度" prop="height">
        <el-input v-model="formData.height" placeholder="请输入商品高度" />
      </el-form-item>
      <el-form-item label="商品体积" prop="volume">
        <el-input v-model="formData.volume" placeholder="请输入商品体积" />
      </el-form-item>
      <el-form-item label="入库质检图片" prop="inspectPicUrls">
        <el-input v-model="formData.inspectPicUrls" placeholder="请输入入库质检图片" />
      </el-form-item>
      <el-form-item label="相关文件地址" prop="fileUrl">
        <el-input v-model="formData.fileUrl" placeholder="请输入相关文件地址" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="入库日期" prop="inTime">
        <el-date-picker
          v-model="formData.inTime"
          type="date"
          value-format="x"
          placeholder="选择入库日期"
        />
      </el-form-item>
      <el-form-item label="到期日期" prop="expiredTime">
        <el-date-picker
          v-model="formData.expiredTime"
          type="date"
          value-format="x"
          placeholder="选择到期日期"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { StockApi, StockVO } from '@/api/mall/agent/stock'
import { WarehouseApi, WarehouseVO } from '@/api/mall/agent/warehouse'

/** 代购仓库 表单 */
defineOptions({ name: 'StockForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  warehouseId: undefined,
  userId: undefined,
  spuId: undefined,
  spuName: undefined,
  skuId: undefined,
  properties: undefined,
  picUrl: undefined,
  count: undefined,
  weight: undefined,
  length: undefined,
  width: undefined,
  height: undefined,
  volume: undefined,
  inspectPicUrls: undefined,
  fileUrl: undefined,
  remark: undefined,
  status: undefined,
  inTime: undefined,
  expiredTime: undefined,
  location: undefined
})
const formRules = reactive({
  userId: [{ required: true, message: '用户编号不能为空', trigger: 'blur' }],
  spuId: [{ required: true, message: 'spu编号不能为空', trigger: 'blur' }],
  spuName: [{ required: true, message: '商品 SPU 名称不能为空', trigger: 'blur' }],
  skuId: [{ required: true, message: '商品 SKU 编号不能为空', trigger: 'blur' }],
  count: [{ required: true, message: '库存数量不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  inTime: [{ required: true, message: '入库日期不能为空', trigger: 'blur' }],
  weight: [{ required: true, message: '商品重量不能为空', trigger: 'blur' }],
  length: [{ required: true, message: '商品长度不能为空', trigger: 'blur' }],
  width: [{ required: true, message: '商品宽度不能为空', trigger: 'blur' }],
  height: [{ required: true, message: '商品高度不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await StockApi.getStock(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as StockVO
    if (formType.value === 'create') {
      await StockApi.createStock(data)
      message.success(t('common.createSuccess'))
    } else {
      await StockApi.updateStock(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    warehouseId: undefined,
    userId: undefined,
    spuId: undefined,
    spuName: undefined,
    skuId: undefined,
    properties: undefined,
    picUrl: undefined,
    count: undefined,
    weight: undefined,
    volume: undefined,
    inspectPicUrls: undefined,
    fileUrl: undefined,
    remark: undefined,
    status: undefined,
    inTime: undefined,
    expiredTime: undefined
  }
  formRef.value?.resetFields()
}

const warehouseList = ref<WarehouseVO[]>([])

onMounted(async () => {
  warehouseList.value = await WarehouseApi.getSimpleWarehouseList()
})
</script>
