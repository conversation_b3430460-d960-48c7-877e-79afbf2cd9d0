<template>
  <Dialog v-model="dialogVisible" title="添加补款单" width="30%">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" label-width="80px">
      <el-form-item label="补款单号" v-if="false">
        <el-input v-model="formData.id" readonly />
      </el-form-item>

      <el-form-item label="补款金额" prop="payPrice">
        <el-input-number
          v-model="formData.payPrice"
          :min="0"
          :precision="2"
          class="mr-2 !w-250px"
          placeholder="请输入补款金额"
        />
        &nbsp;元
      </el-form-item>
      <el-form-item label="补款原因">
        <el-input v-model="formData.reason" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="formData.remark" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as OrderSupplement from '@/api/mall/trade/ordersupplement'
import * as TradeOrderApi from '@/api/mall/trade/order'
import { convertToInteger, formatToFraction } from '@/utils'

defineOptions({ name: 'OrderSupplementForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

const formData = ref<OrderSupplement.OrderSupplementVO>({
  id: null,
  orderId: null, // 订单编号
  payPrice: 0, // 补款金额
  reason: '', // 原因
  remark: '' //备注
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (row: TradeOrderApi.OrderVO) => {
  resetForm()
  if (row.supplementId && row.supplementId > 0) {
    formType.value = 'update'
    const data = await OrderSupplement.OrderSupplementApi.getOrderSupplement(row.supplementId)
    formData.value = {
      ...data,
      payPrice: formatToFraction(data.payPrice)
    }
  } else {
    formType.value = 'create'
    formData.value.orderId = row.id
  }
  dialogVisible.value = true
  console.log('打开。。。。formType.value', formType.value)
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 提交请求
  formLoading.value = true
  try {
    // const data = unref(formData)

    const data = {
      ...formData.value,
      payPrice: convertToInteger(formData.value.payPrice)
    }

    if (formType.value === 'update') {
      await OrderSupplement.OrderSupplementApi.updateOrderSupplement(data)
      message.success(t('common.updateSuccess'))
    } else {
      await OrderSupplement.OrderSupplementApi.createOrderSupplement(data)
      message.success(t('common.createSuccess'))
    }

    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success', true)
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    orderId: null, // 订单编号
    payPrice: 0, // 补款金额
    reason: '', // 原因
    remark: '' //备注
  }
  formRef.value?.resetFields()
}

onMounted(async () => {
  console.log('加载。。。。formData.value.id', formData.value.id)
})
</script>
