<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <!-- <el-form-item label="用户编号" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户编号" />
      </el-form-item>
      <el-form-item label="单据类型" prop="orderType">
        <el-select v-model="formData.orderType" placeholder="请选择单据类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="订单编号" prop="orderId">
        <el-input v-model="formData.orderId" placeholder="请输入订单编号" />
      </el-form-item>
      <!-- <el-form-item label="原支付金额" prop="originalPayPrice">
        <el-input v-model="formData.originalPayPrice" placeholder="请输入原支付金额" />
      </el-form-item>
      <el-form-item label="实际应付金额" prop="actualPayPrice">
        <el-input v-model="formData.actualPayPrice" placeholder="请输入实际应付金额" />
      </el-form-item> -->
      <el-form-item label="需补款金额" prop="payPrice">
        <el-input v-model="formData.payPrice" placeholder="请输入需补款金额" />
      </el-form-item>
      <el-form-item label="补款原因" prop="reason">
        <el-input v-model="formData.reason" placeholder="请输入补款原因(给用户看)" />
      </el-form-item>
      <!-- <el-form-item label="支付状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="支付单号" prop="payOrderId">
        <el-input v-model="formData.payOrderId" placeholder="请输入支付订单编号" />
      </el-form-item>
      <el-form-item label="支付渠道" prop="payChannelCode">
        <el-input v-model="formData.payChannelCode" placeholder="请输入支付成功的支付渠道" />
      </el-form-item>
      <el-form-item label="支付时间" prop="payTime">
        <el-date-picker
          v-model="formData.payTime"
          type="date"
          value-format="x"
          placeholder="选择订单支付时间"
        />
      </el-form-item> -->
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { OrderSupplementApi, OrderSupplementVO } from '@/api/mall/trade/ordersupplement'

/** 订单补款单 表单 */
defineOptions({ name: 'OrderSupplementForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  no: undefined,
  userId: undefined,
  orderType: undefined,
  orderId: undefined,
  originalPayPrice: undefined,
  actualPayPrice: undefined,
  payPrice: undefined,
  reason: undefined,
  status: undefined,
  payOrderId: undefined,
  payChannelCode: undefined,
  payTime: undefined,
  remark: undefined
})
const formRules = reactive({
  // no: [{ required: true, message: '补款单流水号不能为空', trigger: 'blur' }],
  // userId: [{ required: true, message: '用户编号不能为空', trigger: 'blur' }],
  // orderType: [{ required: true, message: '单据类型不能为空', trigger: 'change' }],
  orderId: [{ required: true, message: '订单编号不能为空', trigger: 'blur' }],
  // originalPayPrice: [{ required: true, message: '原支付金额不能为空', trigger: 'blur' }],
  // actualPayPrice: [{ required: true, message: '实际应付金额不能为空', trigger: 'blur' }],
  payPrice: [{ required: true, message: '需补款金额不能为空', trigger: 'blur' }]
  // status: [{ required: true, message: '支付状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrderSupplementApi.getOrderSupplement(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderSupplementVO
    if (formType.value === 'create') {
      await OrderSupplementApi.createOrderSupplement(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrderSupplementApi.updateOrderSupplement(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    no: undefined,
    userId: undefined,
    orderType: undefined,
    orderId: undefined,
    originalPayPrice: undefined,
    actualPayPrice: undefined,
    payPrice: undefined,
    reason: undefined,
    status: undefined,
    payOrderId: undefined,
    payChannelCode: undefined,
    payTime: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
