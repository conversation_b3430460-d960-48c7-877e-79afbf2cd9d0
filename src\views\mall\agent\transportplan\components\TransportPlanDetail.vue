<template>
  <Dialog :title="'国际货运方案详情'" v-model="dialogVisible" width="800px">
    <el-descriptions :column="3" border>
      <el-descriptions-item label="方案名称" :span="1">{{ detailData.name }}</el-descriptions-item>
      <el-descriptions-item label="方案编码" :span="1">{{ detailData.code }}</el-descriptions-item>
      <el-descriptions-item label="渠道编码" :span="1">{{
        detailData.channelCode
      }}</el-descriptions-item>

      <el-descriptions-item label="方案类型" :span="1">
        <dict-tag :type="DICT_TYPE.AGENT_TRANSPORT_PLAN_TYPE" value="detailData.planType" />
      </el-descriptions-item>
      <el-descriptions-item label="货运公司" :span="1">{{
        detailData.companyName
      }}</el-descriptions-item>
      <el-descriptions-item label="运输方式" :span="1">
        <dict-tag :type="DICT_TYPE.AGENT_TRANSPORT_METHOD" value="detailData.transportMethod" />
      </el-descriptions-item>

      <el-descriptions-item label="关键词" :span="1">{{
        detailData.keywords
      }}</el-descriptions-item>
      <el-descriptions-item label="推荐品类" :span="1">{{
        detailData.recommendCategories
      }}</el-descriptions-item>
      <el-descriptions-item label="是否带电" :span="1">
        <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" value="detailData.battery" />
      </el-descriptions-item>
    </el-descriptions>

    <el-divider content-position="left">
      <el-icon><Money /></el-icon> 价格信息
    </el-divider>

    <el-descriptions :column="3" border>
      <el-descriptions-item label="基础运费" :span="1">
        {{ detailData.basePrice ? (detailData.basePrice / 100).toFixed(2) + '元' : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="附加费用" :span="1">
        {{ detailData.attachPrice ? (detailData.attachPrice / 100).toFixed(2) + '元' : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="首重" :span="1">
        {{ detailData.firstWeight ? detailData.firstWeight + 'g' : '-' }}
      </el-descriptions-item>

      <el-descriptions-item label="首重价格" :span="1">
        {{
          detailData.firstWeightPrice ? (detailData.firstWeightPrice / 100).toFixed(2) + '元' : '-'
        }}
      </el-descriptions-item>
      <el-descriptions-item label="续重单位" :span="1">
        {{ detailData.additionalWeight ? detailData.additionalWeight + 'g' : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="续重价格" :span="1">
        {{
          detailData.additionalWeightPrice
            ? (detailData.additionalWeightPrice / 100).toFixed(2) + '元'
            : '-'
        }}
      </el-descriptions-item>

      <el-descriptions-item label="最低计费重量" :span="1">
        {{ detailData.minWeight ? detailData.minWeight + 'g' : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="最高重量" :span="1">
        {{ detailData.maxWeight ? detailData.maxWeight + 'g' : '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="体积重量比例" :span="1">
        {{ detailData.volumetricWeightRatio ? detailData.volumetricWeightRatio + 'cm³/kg' : '-' }}
      </el-descriptions-item>
    </el-descriptions>

    <el-divider content-position="left">
      <el-icon><InfoFilled /></el-icon> 其他信息
    </el-divider>

    <el-descriptions :column="3" border>
      <el-descriptions-item label="支持的国家" :span="1">{{
        detailData.countries || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="附加服务" :span="1">{{
        detailData.attachServices || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="排序" :span="1">{{ detailData.sort }}</el-descriptions-item>

      <el-descriptions-item label="生效时间" :span="1">
        {{ formatDate(detailData.effectiveTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="状态" :span="1">
        <dict-tag :type="DICT_TYPE.COMMON_STATUS" value="detailData.status" />
      </el-descriptions-item>
      <el-descriptions-item label="创建时间" :span="1">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>

      <el-descriptions-item label="描述" :span="3">
        <div class="whitespace-pre-wrap">{{ detailData.description || '-' }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="备注" :span="3">
        <div class="whitespace-pre-wrap">{{ detailData.remark || '-' }}</div>
      </el-descriptions-item>
    </el-descriptions>

    <el-divider content-position="left">
      <el-icon><Tickets /></el-icon> 价格方案明细
    </el-divider>

    <TransportPlanFeeList :plan-id="planId" :is-detail="true" />

    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { TransportPlanApi, TransportPlanVO } from '@/api/mall/agent/transportplan'
import { formatDate } from '@/utils/formatTime'
import { Money, InfoFilled, Tickets } from '@element-plus/icons-vue'
import TransportPlanFeeList from './TransportPlanFeeList.vue'

/** 国际货运方案详情 */
defineOptions({ name: 'TransportPlanDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 详情的加载中
const detailData = ref<TransportPlanVO>({} as TransportPlanVO) // 详情数据
const planId = ref() // 方案编号 需要初始化即赋值，赋值明细表无法加载 ding

/** 打开弹窗 */
const open = async (id: number) => {
  planId.value = id
  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    detailData.value = await TransportPlanApi.getTransportPlan(id)
  } finally {
    detailLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
.whitespace-pre-wrap {
  white-space: pre-wrap;
}

:deep(.el-descriptions__label) {
  width: 120px;
  font-weight: bold;
}

:deep(.el-descriptions__content) {
  padding: 8px 12px;
}

:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
}

:deep(.el-divider) {
  margin: 20px 0;
}
</style>
