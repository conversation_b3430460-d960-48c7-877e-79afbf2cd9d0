<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="分类编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入分类编码,用于前端路由" />
      </el-form-item>

      <el-form-item label="图标" prop="icon">
        <el-input v-model="formData.icon" placeholder="请输入图标代码" />
      </el-form-item>
      <el-form-item label="推荐" prop="faq">
        <el-radio-group v-model="formData.recommend">
          <el-radio :value="true">是</el-radio>
          <el-radio :value="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="formData.sort"
          :min="0"
          :precision="0"
          :step="1"
          placeholder="请输入排序"
        />
      </el-form-item>

      <el-form-item label="中文标题" prop="titleZh">
        <el-input v-model="formData.titleZh" placeholder="请输入中文标题" />
      </el-form-item>
      <el-form-item label="英文标题" prop="titleEn">
        <el-input v-model="formData.titleEn" placeholder="请输入英文标题" />
      </el-form-item>
      <el-form-item label="法语标题" prop="titleFr">
        <el-input v-model="formData.titleFr" placeholder="请输入法语标题" />
      </el-form-item>
      <el-form-item label="德语标题" prop="titleDe">
        <el-input v-model="formData.titleDe" placeholder="请输入德语标题" />
      </el-form-item>
      <el-form-item label="西班牙语标题" prop="titleEs">
        <el-input v-model="formData.titleEs" placeholder="请输入西班牙语标题" />
      </el-form-item>
      <el-form-item label="阿拉伯语标题" prop="titleAr">
        <el-input v-model="formData.titleAr" placeholder="请输入阿拉伯语标题" />
      </el-form-item>
      <el-form-item label="中文描述" prop="descriptionZh" class="full-width">
        <el-input
          v-model="formData.descriptionZh"
          placeholder="请输入中文描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="英文描述" prop="descriptionEn" class="full-width">
        <el-input
          v-model="formData.descriptionEn"
          placeholder="请输入英文描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="法语描述" prop="descriptionFr" class="full-width">
        <el-input
          v-model="formData.descriptionFr"
          placeholder="请输入法语描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="德语描述" prop="descriptionDe" class="full-width">
        <el-input
          v-model="formData.descriptionDe"
          placeholder="请输入德语描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="西班牙语描述" prop="descriptionEs" class="full-width">
        <el-input
          v-model="formData.descriptionEs"
          placeholder="请输入西班牙语描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="阿拉伯语描述" prop="descriptionAr" class="full-width">
        <el-input
          v-model="formData.descriptionAr"
          placeholder="请输入阿拉伯语描述"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { HelpCategoryApi, HelpCategoryVO } from '@/api/mall/promotion/helpcategory'

/** 帮助分类 表单 */
defineOptions({ name: 'HelpCategoryForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  icon: undefined,
  titleZh: undefined,
  titleEn: undefined,
  titleFr: undefined,
  titleDe: undefined,
  titleEs: undefined,
  titleAr: undefined,
  descriptionZh: undefined,
  descriptionEn: undefined,
  descriptionFr: undefined,
  descriptionDe: undefined,
  descriptionEs: undefined,
  descriptionAr: undefined,
  status: 0, // 默认开启
  sort: 0, // 默认排序为0
  recommend: false // 默认不推荐
})
const formRules = reactive({
  code: [{ required: true, message: '分类编码不能为空', trigger: 'blur' }],
  titleZh: [{ required: true, message: '中文标题不能为空', trigger: 'blur' }],
  titleEn: [{ required: true, message: '英文标题不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  recommend: [{ required: true, message: '推荐能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await HelpCategoryApi.getHelpCategory(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as HelpCategoryVO
    if (formType.value === 'create') {
      await HelpCategoryApi.createHelpCategory(data)
      message.success(t('common.createSuccess'))
    } else {
      await HelpCategoryApi.updateHelpCategory(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    icon: undefined,
    titleZh: undefined,
    titleEn: undefined,
    titleFr: undefined,
    titleDe: undefined,
    titleEs: undefined,
    titleAr: undefined,
    descriptionZh: undefined,
    descriptionEn: undefined,
    descriptionFr: undefined,
    descriptionDe: undefined,
    descriptionEs: undefined,
    descriptionAr: undefined,
    status: 0, // 默认开启
    sort: 0, // 默认排序为0
    recommend: false // 默认不推荐
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.form-container {
  display: flex;
  flex-wrap: wrap;
}

.form-container :deep(.el-form-item) {
  width: 48%;
  margin-right: 2%;
}

.form-container :deep(.full-width) {
  width: 100%;
}

.form-container :deep(.el-textarea__inner) {
  width: 100%;
}
</style>
