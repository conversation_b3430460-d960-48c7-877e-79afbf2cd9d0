<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      :inline="true"
      class="form-container"
    >
      <el-form-item label="分类" prop="categoryId">
        <!-- <el-input v-model="formData.categoryId" placeholder="请输入分类编号" /> -->
        <el-select v-model="formData.categoryId" class="w-80!" placeholder="请选择帮助分类">
          <el-option
            v-for="item in categoryList"
            :key="item.id"
            :label="item.titleZh"
            :value="item.id as number"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文章编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入文章编码用于前端路由" />
      </el-form-item>
      <el-form-item label="文章作者" prop="author">
        <el-input v-model="formData.author" placeholder="请输入文章作者" />
      </el-form-item>
      <el-form-item label="浏览次数" prop="browseCount">
        <el-input v-model="formData.browseCount" placeholder="请输入浏览次数" />
      </el-form-item>
      <el-form-item label="中文标题" prop="titleZh">
        <el-input v-model="formData.titleZh" placeholder="请输入中文标题" />
      </el-form-item>
      <el-form-item label="英文标题" prop="titleEn">
        <el-input v-model="formData.titleEn" placeholder="请输入英文标题" />
      </el-form-item>
      <el-form-item label="法语标题" prop="titleFr">
        <el-input v-model="formData.titleFr" placeholder="请输入法语标题" />
      </el-form-item>
      <el-form-item label="德语标题" prop="titleDe">
        <el-input v-model="formData.titleDe" placeholder="请输入德语标题" />
      </el-form-item>
      <el-form-item label="西班牙语标题" prop="titleEs">
        <el-input v-model="formData.titleEs" placeholder="请输入西班牙语标题" />
      </el-form-item>
      <el-form-item label="阿拉伯语标题" prop="titleAr">
        <el-input v-model="formData.titleAr" placeholder="请输入阿拉伯语标题" />
      </el-form-item>
      <el-form-item label="文章内容" prop="content" class="full-width">
        <el-tabs v-model="activeContentTab">
          <el-tab-pane label="中文内容" name="zh">
            <div v-if="activeContentTab === 'zh'">
              <Editor v-model="formData.contentZh" height="300px" editorId="editor-content" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="英文内容" name="en">
            <div v-if="activeContentTab === 'en'">
              <Editor v-model="formData.contentEn" height="300px" editorId="editor-content" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="法语内容" name="fr">
            <div v-if="activeContentTab === 'fr'">
              <Editor v-model="formData.contentFr" height="300px" editorId="editor-content" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="德语内容" name="de">
            <div v-if="activeContentTab === 'de'">
              <Editor v-model="formData.contentDe" height="300px" editorId="editor-content" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="西班牙语内容" name="es">
            <div v-if="activeContentTab === 'es'">
              <Editor v-model="formData.contentEs" height="300px" editorId="editor-content" />
            </div>
          </el-tab-pane>
          <el-tab-pane label="阿拉伯语内容" name="ar">
            <div v-if="activeContentTab === 'ar'">
              <Editor v-model="formData.contentAr" height="300px" editorId="editor-content" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form-item>
      <el-form-item label="相关文章" prop="relatedArticles">
        <el-input v-model="formData.relatedArticles" placeholder="请输入相关文章" />
      </el-form-item>
      <el-form-item label="有帮助反馈" prop="helpfulCount">
        <el-input v-model="formData.helpfulCount" placeholder="请输入有帮助反馈数量" />
      </el-form-item>
      <el-form-item label="无帮助反馈" prop="unhelpfulCount">
        <el-input v-model="formData.unhelpfulCount" placeholder="请输入无帮助反馈数量" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="formData.sort"
          :min="0"
          :precision="0"
          :step="1"
          placeholder="请输入排序"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否FAQ" prop="faq">
        <el-radio-group v-model="formData.faq">
          <el-radio
            v-for="dict in getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value as string"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE, getBoolDictOptions } from '@/utils/dict'
import { HelpArticleApi, HelpArticleVO } from '@/api/mall/promotion/helparticle'
import * as HelpCategory from '@/api/mall/promotion/helpcategory'
import { Editor } from '@/components/Editor'

/** 帮助文章 表单 */
defineOptions({ name: 'HelpArticleForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const activeContentTab = ref('zh') // 当前选中的内容标签页
const formData = ref({
  id: undefined,
  categoryId: undefined,
  code: undefined,
  author: undefined,
  browseCount: 0,
  titleZh: undefined,
  titleEn: undefined,
  titleFr: undefined,
  titleDe: undefined,
  titleEs: undefined,
  titleAr: undefined,
  contentZh: '',
  contentEn: '',
  contentFr: '',
  contentDe: '',
  contentEs: '',
  contentAr: '',
  relatedArticles: undefined,
  helpfulCount: 0,
  unhelpfulCount: 0,
  sort: 0,
  status: 0, // 默认开启
  faq: false
})
const formRules = reactive({
  categoryId: [{ required: true, message: '分类编号不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '文章编码不能为空', trigger: 'blur' }],
  titleZh: [{ required: true, message: '中文标题不能为空', trigger: 'blur' }],
  titleEn: [{ required: true, message: '英文标题不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  faq: [{ required: true, message: '是否FAQ不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 默认激活第一个tab（中文内容）
  activeContentTab.value = 'zh'
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await HelpArticleApi.getHelpArticle(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 获取分类列表*/
const categoryList = ref()

onMounted(async () => {
  // 获得分类
  const data = await HelpCategory.HelpCategoryApi.getHelpCategoryList()
  categoryList.value = data
})

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as HelpArticleVO
    if (formType.value === 'create') {
      await HelpArticleApi.createHelpArticle(data)
      message.success(t('common.createSuccess'))
    } else {
      await HelpArticleApi.updateHelpArticle(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    categoryId: undefined,
    code: undefined,
    author: undefined,
    browseCount: 0,
    titleZh: undefined,
    titleEn: undefined,
    titleFr: undefined,
    titleDe: undefined,
    titleEs: undefined,
    titleAr: undefined,
    contentZh: '',
    contentEn: '',
    contentFr: '',
    contentDe: '',
    contentEs: '',
    contentAr: '',
    relatedArticles: undefined,
    helpfulCount: 0,
    unhelpfulCount: 0,
    sort: 0,
    status: 0, // 默认开启
    faq: false
  }
  // 重置为中文内容tab
  activeContentTab.value = 'zh'
  formRef.value?.resetFields()
}
</script>

<style scoped>
.form-container {
  display: flex;
  flex-wrap: wrap;
}

.form-container :deep(.el-form-item) {
  width: 48%;
  margin-right: 2%;
}

.form-container :deep(.full-width) {
  width: 100%;
}

.form-container :deep(.el-tabs__content) {
  padding: 15px 0;
}

.form-container :deep(.el-textarea__inner) {
  width: 100%;
}
</style>
